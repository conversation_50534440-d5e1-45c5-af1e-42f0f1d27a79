newDashboard:
  variations:
    enabled: true
    disabled: false
  defaultRule:
    variation: disabled
  targeting:
    - query: "beta == true"
      variation: enabled
    - query: "beta == false"
      variation: disabled

enhancedLogging:
  variations:
    on: true
    off: false
  defaultRule:
    variation: off
  targeting:
    - query: 'role == "admin"'
      variation: on

paymentMethodV2:
  variations:
    v2: "payment-v2"
    v1: "payment-v1"
  defaultRule:
    variation: v1
  targeting:
    - query: 'plan == "premium"'
      percentage:
        v2: 80
        v1: 20
    - query: 'plan == "basic"'
      percentage:
        v2: 20
        v1: 80

apiRateLimit:
  variations:
    high: 1000
    medium: 500
    low: 100
  defaultRule:
    variation: medium
  targeting:
    - query: 'plan == "premium"'
      percentage:
        high: 100
        medium: 0
        low: 0
    - query: 'plan == "basic"'
      percentage:
        high: 0
        medium: 100
        low: 0

allowToAdjust:
  variations:
    enabled: true
    disabled: false
  defaultRule:
    variation: disabled
  targeting:
    - query: 'clientCode IN ["CL32655", "CL32607", "CL32654", "CL29542", "CL29517", "CL29506", "CL29505"]'
      variation: enabled
