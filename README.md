# Go Feature Flag Demo with Echo and OpenFeature

This is an example web HTTP backend using Echo framework with go-feature-flag abstracted through OpenFeature, using YAML configuration.

## Features

- **Echo Framework**: Lightweight HTTP web framework
- **Go Feature Flag**: Feature flag management with YAML configuration
- **OpenFeature**: Vendor-agnostic feature flag API
- **Multiple Flag Types**: Boolean, string, and integer flags
- **User Targeting**: Role-based and plan-based targeting rules
- **Percentage Rollouts**: Gradual feature rollouts

## Setup

1. Install dependencies:
```bash
go mod tidy
```

2. Run the server:
```bash
go run main.go
```

The server will start on `http://localhost:8080`

## API Endpoints

### Base Endpoint
- `GET /` - Health check

### Feature Flag Endpoints
All endpoints accept query parameters: `role`, `plan`, `beta`

- `GET /dashboard/:userId` - Check new dashboard feature
- `GET /logging/:userId` - Check enhanced logging feature  
- `GET /payment/:userId` - Get payment method version
- `GET /rate-limit/:userId` - Get API rate limit
- `GET /flags/:userId` - Get all flags for user

## Example Usage

```bash
# Check dashboard for beta user
curl "http://localhost:8080/dashboard/user123?role=user&plan=premium&beta=true"

# Check admin logging
curl "http://localhost:8080/logging/admin1?role=admin&plan=premium"

# Get payment method for basic user
curl "http://localhost:8080/payment/user456?role=user&plan=basic"

# Get rate limit for premium user
curl "http://localhost:8080/rate-limit/user789?role=user&plan=premium"

# Get all flags
curl "http://localhost:8080/flags/user123?role=user&plan=premium&beta=true"
```

## Configuration

Feature flags are configured in `flags.yaml`:

- **new-dashboard**: Boolean flag with beta user targeting
- **enhanced-logging**: Boolean flag for admin users
- **payment-method-v2**: String flag with plan-based rollout
- **api-rate-limit**: Integer flag with different limits per plan

## User Context

The application uses these user attributes for targeting:
- `role`: User role (admin, user, etc.)
- `plan`: Subscription plan (premium, basic, etc.)
- `beta`: Beta program participation (true/false)