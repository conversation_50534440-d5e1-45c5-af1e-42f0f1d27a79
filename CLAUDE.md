# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Run

- `go mod tidy` - Install/update dependencies
- `go run main.go` - Run the development server on localhost:8080
- `go build -o poc-go-feature-flag main.go` - Build executable binary

### Testing

This project currently has no test files. Standard Go testing would use:

- `go test ./...` - Run all tests (when tests exist)
- `go test -v ./...` - Run tests with verbose output

## Architecture Overview

This is a Go HTTP API demo using Echo framework with go-feature-flag for feature flag management. The architecture follows a simple single-file structure:

### Core Components

- **Echo HTTP Server**: Lightweight web framework handling REST endpoints
- **go-feature-flag**: Feature flag evaluation with YAML configuration
- **FeatureFlagService**: Service layer wrapping flag operations

### Key Files

- `main.go`: Contains entire application - server setup, service, and all endpoints
- `flags.yaml`: Feature flag configurations with targeting rules and rollout percentages
- `go.mod`: Dependencies including Echo v4 and go-feature-flag v1.45.5

### API Structure

All endpoints follow pattern `/{feature}/{userId}` with query parameters for user context:

- `role`: User role (admin, user)
- `plan`: Subscription plan (premium, basic)
- `beta`: Beta program participation (true/false)

### Feature Flag Types

- **Boolean flags**: `new-dashboard`, `enhanced-logging`
- **String flags**: `payment-method-v2` (returns "payment-v1" or "payment-v2")
- **Integer flags**: `api-rate-limit` (returns rate limit values)

### User Context Pattern

All endpoints create a `User` struct with ID, Role, Plan, and Beta fields, then build an evaluation context for flag evaluation with these attributes.

### Flag Configuration

The `flags.yaml` uses go-feature-flag YAML format with:

- `variations`: Possible flag values
- `defaultRule`: Fallback variation
- `targeting`: Rules with queries and percentage rollouts
